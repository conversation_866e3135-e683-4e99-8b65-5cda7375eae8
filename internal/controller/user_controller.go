package controller

import (
	"errors"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/oapi-codegen/runtime/types"
	"gorm.io/gorm"

	api "github.com/smooth-inc/backend/api/generated"
	"github.com/smooth-inc/backend/internal/domain"
	"github.com/smooth-inc/backend/internal/infra/http/response"
	"github.com/smooth-inc/backend/internal/infra/logger"
	"github.com/smooth-inc/backend/internal/usecase"
)

type UserController struct {
	userUsecase  usecase.UserUsecase
	plateUsecase usecase.PlateUsecase
	logger       *logger.Logger
}

func NewUserController(userUsecase usecase.UserUsecase, plateUsecase usecase.PlateUsecase, logger *logger.Logger) *UserController {
	return &UserController{
		userUsecase:  userUsecase,
		plateUsecase: plateUsecase,
		logger:       logger,
	}
}

func (uc *UserController) GetUsersProfile(c *gin.Context) {
	uc.logger.LogInfo(c.Request.Context(), "Getting user profile", map[string]interface{}{
		"method": "GET",
		"path":   "/users/profile",
	})

	userID, err := GetUserIDFromContext(c)
	if err != nil {
		uc.logger.LogError(c.Request.Context(), err, "Failed to get user ID from context")
		response.Unauthorized(c, "UNAUTHORIZED", "Invalid user authentication")
		return
	}

	user, err := uc.userUsecase.GetByID(c.Request.Context(), userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			uc.logger.LogWarn(c.Request.Context(), "User not found", map[string]interface{}{
				"user_id": userID,
			})
			response.NotFound(c, "USER_NOT_FOUND", "User not found")
			return
		}

		uc.logger.LogError(c.Request.Context(), err, "Failed to retrieve user profile", map[string]interface{}{
			"user_id": userID,
		})
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to retrieve user profile")
		return
	}

	uc.logger.LogInfo(c.Request.Context(), "Successfully retrieved user profile", map[string]interface{}{
		"user_id": userID,
	})

	apiResponse := uc.mapUserToAPIResponse(user)
	response.Success(c, apiResponse)
}

func (uc *UserController) PutUsersProfile(c *gin.Context) {
	uc.logger.LogInfo(c.Request.Context(), "Updating user profile", map[string]interface{}{
		"method": "PUT",
		"path":   "/users/profile",
	})

	userID, err := GetUserIDFromContext(c)
	if err != nil {
		uc.logger.LogError(c.Request.Context(), err, "Failed to get user ID from context")
		response.Unauthorized(c, "UNAUTHORIZED", "Invalid user authentication")
		return
	}

	var req api.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		uc.logger.LogError(c.Request.Context(), err, "Failed to bind request", map[string]interface{}{
			"user_id": userID,
		})
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request body", nil)
		return
	}

	var preferredLanguage *domain.LanguageCode
	if req.PreferredLanguage != nil {
		lang := domain.LanguageCode(*req.PreferredLanguage)
		preferredLanguage = &lang
	}

	var email *string
	if req.Email != nil {
		emailStr := string(*req.Email)
		email = &emailStr
	}

	updatedUser, err := uc.userUsecase.UpdateExtendedProfile(
		c.Request.Context(),
		userID,
		req.Name,
		req.Phone,
		email,
		req.Username,
		preferredLanguage,
		req.DefaultPaymentMethodId,
		req.AutoPaymentEnabled,
		req.NotifyEmail,
		req.NotifyPush,
	)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			uc.logger.LogWarn(c.Request.Context(), "User not found for profile update", map[string]interface{}{
				"user_id": userID,
			})
			response.NotFound(c, "USER_NOT_FOUND", "User not found")
			return
		}

		if strings.Contains(err.Error(), "email already exists") {
			uc.logger.LogWarn(c.Request.Context(), "Email already exists", map[string]interface{}{
				"user_id": userID,
				"email":   email,
			})
			response.Conflict(c, "EMAIL_ALREADY_EXISTS", "Email address is already in use", nil)
			return
		}

		if strings.Contains(err.Error(), "username already exists") {
			uc.logger.LogWarn(c.Request.Context(), "Username already exists", map[string]interface{}{
				"user_id":  userID,
				"username": req.Username,
			})
			response.Conflict(c, "USERNAME_ALREADY_EXISTS", "Username is already in use", nil)
			return
		}

		if strings.Contains(err.Error(), "validation") {
			uc.logger.LogWarn(c.Request.Context(), "Profile update validation failed", map[string]interface{}{
				"user_id": userID,
				"error":   err.Error(),
			})
			response.BadRequest(c, "VALIDATION_ERROR", err.Error(), nil)
			return
		}

		uc.logger.LogError(c.Request.Context(), err, "Failed to update user profile", map[string]interface{}{
			"user_id": userID,
		})
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to update user profile")
		return
	}

	uc.logger.LogInfo(c.Request.Context(), "Successfully updated user profile", map[string]interface{}{
		"user_id": userID,
	})

	apiResponse := uc.mapUserToAPIResponse(updatedUser)
	response.Success(c, apiResponse)
}

// GetUsersPlates handles GET /users/plates
func (uc *UserController) GetUsersPlates(c *gin.Context) {
	uc.logger.LogInfo(c.Request.Context(), "Getting user plates", map[string]interface{}{
		"method": "GET",
		"path":   "/users/plates",
	})

	userID, err := GetUserIDFromContext(c)
	if err != nil {
		uc.logger.LogError(c.Request.Context(), err, "Failed to get user ID from context")
		response.Unauthorized(c, "UNAUTHORIZED", "Invalid user authentication")
		return
	}

	plates, err := uc.plateUsecase.GetByUserID(c.Request.Context(), userID)
	if err != nil {
		uc.logger.LogError(c.Request.Context(), err, "Failed to get user plates", map[string]interface{}{
			"user_id": userID,
		})
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to retrieve user plates")
		return
	}

	apiResponse := make([]api.Plate, len(plates))
	for i, plate := range plates {
		apiResponse[i] = *uc.mapPlateToAPIResponse(plate)
	}

	uc.logger.LogInfo(c.Request.Context(), "Successfully retrieved user plates", map[string]interface{}{
		"user_id":     userID,
		"plate_count": len(plates),
	})

	response.Success(c, apiResponse)
}

// PostUsersPlates handles POST /users/plates
func (uc *UserController) PostUsersPlates(c *gin.Context) {
	uc.logger.LogInfo(c.Request.Context(), "Creating user plate", map[string]interface{}{
		"method": "POST",
		"path":   "/users/plates",
	})

	userID, err := GetUserIDFromContext(c)
	if err != nil {
		uc.logger.LogError(c.Request.Context(), err, "Failed to get user ID from context")
		response.Unauthorized(c, "UNAUTHORIZED", "Invalid user authentication")
		return
	}

	var request api.CreatePlateRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		uc.logger.LogError(c.Request.Context(), err, "Failed to bind request", map[string]interface{}{
			"user_id": userID,
		})
		response.BadRequest(c, "VALIDATION_ERROR", "Invalid request format", nil)
		return
	}

	if err := uc.validateCreatePlateRequest(&request); err != nil {
		uc.logger.LogError(c.Request.Context(), err, "Request validation failed", map[string]interface{}{
			"user_id": userID,
		})
		response.BadRequest(c, "VALIDATION_ERROR", err.Error(), nil)
		return
	}

	plateType := domain.PlateTypeNormal
	if request.PlateType != nil {
		plateType = domain.PlateType(*request.PlateType)
	}

	plate, err := uc.plateUsecase.Create(
		c.Request.Context(),
		userID,
		request.Region,
		request.Classification,
		request.Hiragana,
		request.SerialNumber,
		plateType,
	)
	if err != nil {
		uc.logger.LogError(c.Request.Context(), err, "Failed to create plate", map[string]interface{}{
			"user_id": userID,
		})

		if strings.Contains(err.Error(), "validation") || strings.Contains(err.Error(), "invalid") {
			response.BadRequest(c, "VALIDATION_ERROR", err.Error(), nil)
			return
		}

		if strings.Contains(err.Error(), "already exists") {
			response.Conflict(c, "PLATE_EXISTS", "Plate number already exists", nil)
			return
		}

		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to create plate")
		return
	}

	apiResponse := uc.mapPlateToAPIResponse(plate)

	uc.logger.LogInfo(c.Request.Context(), "Successfully created plate", map[string]interface{}{
		"user_id":      userID,
		"plate_id":     plate.ID,
		"plate_number": plate.PlateNumber,
	})

	response.Created(c, apiResponse)
}

// DeleteUsersPlatesPlateId handles DELETE /users/plates/{plateId}
func (uc *UserController) DeleteUsersPlatesPlateId(c *gin.Context, plateId types.UUID) {
	uc.logger.LogInfo(c.Request.Context(), "Deleting user plate", map[string]interface{}{
		"method":   "DELETE",
		"path":     "/users/plates/{plateId}",
		"plate_id": plateId,
	})

	userID, err := GetUserIDFromContext(c)
	if err != nil {
		uc.logger.LogError(c.Request.Context(), err, "Failed to get user ID from context")
		response.Unauthorized(c, "UNAUTHORIZED", "Invalid user authentication")
		return
	}

	plateUUID := uuid.UUID(plateId)
	if err := uc.plateUsecase.Delete(c.Request.Context(), plateUUID, userID); err != nil {
		uc.logger.LogError(c.Request.Context(), err, "Failed to delete plate", map[string]interface{}{
			"user_id":  userID,
			"plate_id": plateId,
		})

		if strings.Contains(err.Error(), "not found") {
			response.NotFound(c, "PLATE_NOT_FOUND", "Plate not found")
			return
		}

		if strings.Contains(err.Error(), "unauthorized") {
			response.Forbidden(c, "FORBIDDEN", "You don't have permission to delete this plate")
			return
		}

		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to delete plate")
		return
	}

	uc.logger.LogInfo(c.Request.Context(), "Successfully deleted plate", map[string]interface{}{
		"user_id":  userID,
		"plate_id": plateId,
	})

	response.NoContent(c)
}

// Helper methods

// mapUserToAPIResponse converts domain User to API User response
func (uc *UserController) mapUserToAPIResponse(user *domain.User) *api.User {
	response := &api.User{
		Id:                     (*types.UUID)(&user.ID),
		Username:               &user.Username,
		Email:                  (*types.Email)(&user.Email),
		Name:                   &user.Name,
		Phone:                  user.Phone,
		PreferredLanguage:      (*api.UserPreferredLanguage)(&user.PreferredLanguage),
		Role:                   (*api.UserRole)(&user.Role),
		Status:                 (*api.UserStatus)(&user.Status),
		StripeCustomerId:       user.StripeCustomerID,
		DefaultPaymentMethodId: user.DefaultPaymentMethodID,
		AutoPaymentEnabled:     &user.AutoPaymentEnabled,
		NotifyEmail:            &user.NotifyEmail,
		NotifyPush:             &user.NotifyPush,
		EmailVerified:          &user.EmailVerified,
		LastLoginAt:            user.LastLoginAt,
		CreatedAt:              &user.CreatedAt,
		UpdatedAt:              &user.UpdatedAt,
	}

	return response
}

// validateCreatePlateRequest validates the create plate request
func (uc *UserController) validateCreatePlateRequest(request *api.CreatePlateRequest) error {
	if strings.TrimSpace(request.Region) == "" {
		return errors.New("region is required")
	}
	if strings.TrimSpace(request.Classification) == "" {
		return errors.New("classification is required")
	}
	if strings.TrimSpace(request.Hiragana) == "" {
		return errors.New("hiragana is required")
	}
	if strings.TrimSpace(request.SerialNumber) == "" {
		return errors.New("serial number is required")
	}
	return nil
}

// mapPlateToAPIResponse converts domain Plate to API Plate response
func (uc *UserController) mapPlateToAPIResponse(plate *domain.Plate) *api.Plate {
	response := &api.Plate{
		Id:             (*types.UUID)(&plate.ID),
		UserId:         (*types.UUID)(&plate.UserID),
		Region:         &plate.Region,
		Classification: &plate.Classification,
		Hiragana:       &plate.Hiragana,
		SerialNumber:   &plate.SerialNumber,
		PlateNumber:    &plate.PlateNumber,
		PlateType:      (*api.PlateType)(&plate.PlateType),
		IsActive:       &plate.IsActive,
		CreatedAt:      &plate.CreatedAt,
		UpdatedAt:      &plate.UpdatedAt,
	}

	return response
}
