package usecase

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"

	"github.com/smooth-inc/backend/internal/domain"
	"github.com/smooth-inc/backend/internal/gateway/stripe"
	"github.com/smooth-inc/backend/internal/repository"
	"github.com/smooth-inc/backend/pkg/errors"
)

type paymentUsecase struct {
	paymentRepo       repository.PaymentRepository
	sessionRepo       repository.SessionRepository
	userRepo          repository.UserRepository
	paymentMethodRepo repository.PaymentMethodRepository
	disputeRepo       repository.DisputeRepository
	invoiceRepo       repository.InvoiceRepository
	notificationRepo  repository.NotificationRepository
	stripeGateway     stripe.Gateway
	emailService      EmailTemplateService
	paymentMethodUC   PaymentMethodUsecase
}

func NewPaymentUsecase(
	paymentRepo repository.PaymentRepository,
	sessionRepo repository.SessionRepository,
	userRepo repository.UserRepository,
	paymentMethodRepo repository.PaymentMethodRepository,
	disputeRepo repository.DisputeRepository,
	invoiceRepo repository.InvoiceRepository,
	notificationRepo repository.NotificationRepository,
	stripeGateway stripe.Gateway,
	emailService EmailTemplateService,
	paymentMethodUC PaymentMethodUsecase,
) PaymentUsecase {
	return &paymentUsecase{
		paymentRepo:       paymentRepo,
		sessionRepo:       sessionRepo,
		userRepo:          userRepo,
		paymentMethodRepo: paymentMethodRepo,
		disputeRepo:       disputeRepo,
		invoiceRepo:       invoiceRepo,
		notificationRepo:  notificationRepo,
		stripeGateway:     stripeGateway,
		emailService:      emailService,
		paymentMethodUC:   paymentMethodUC,
	}
}

func (uc *paymentUsecase) CreatePaymentLink(ctx context.Context, sessionID uuid.UUID) (string, error) {
	session, err := uc.sessionRepo.GetByID(ctx, sessionID)
	if err != nil {
		return "", errors.NewNotFoundError("session not found")
	}

	if session.Status != domain.SessionStatusCompleted {
		return "", errors.NewBadRequestError("session must be completed to create payment link")
	}

	existingPayment, err := uc.paymentRepo.GetBySessionID(ctx, sessionID)
	if err == nil && existingPayment != nil {
		if existingPayment.StripePaymentLinkID != nil {
			return *existingPayment.StripePaymentLinkID, nil
		}
	}

	if session.UserID == nil {
		return "", errors.NewBadRequestError("session has no associated user")
	}

	amount := session.GetFinalAmount()
	if amount <= 0 {
		return "", errors.NewBadRequestError("invalid session fee amount")
	}

	payment, err := domain.NewPayment(sessionID, *session.UserID, amount)
	if err != nil {
		return "", errors.NewValidationError(err.Error())
	}

	paymentLink, err := uc.stripeGateway.CreatePaymentLink(ctx, amount, "JPY", fmt.Sprintf("Parking fee for session %s", sessionID.String()))
	if err != nil {
		return "", errors.NewExternalServiceError("failed to create Stripe payment link", err)
	}

	payment.SetStripePaymentLinkID(paymentLink.ID)
	if paymentLink.PaymentIntentID != nil {
		payment.SetStripePaymentIntentID(*paymentLink.PaymentIntentID)
	}

	if existingPayment != nil {
		payment.ID = existingPayment.ID
		if err := uc.paymentRepo.Update(ctx, payment); err != nil {
			return "", errors.NewDatabaseError("failed to update payment", err)
		}
	} else {
		if err := uc.paymentRepo.Create(ctx, payment); err != nil {
			return "", errors.NewDatabaseError("failed to create payment", err)
		}
	}

	return paymentLink.URL, nil
}

func (uc *paymentUsecase) ProcessWebhook(ctx context.Context, payload []byte, signature string) error {
	event, err := uc.stripeGateway.VerifyWebhook(ctx, payload, signature)
	if err != nil {
		return errors.NewBadRequestError("invalid webhook signature")
	}

	switch event.Type {
	case "charge.dispute.created":
		return uc.handleChargeDisputeCreated(ctx, event.Data)
	case "charge.failed":
		return uc.handleChargeFailed(ctx, event.Data)
	case "charge.succeeded":
		return uc.handleChargeSucceeded(ctx, event.Data)
	case "invoice.payment_failed":
		return uc.handleInvoicePaymentFailed(ctx, event.Data)
	case "invoice.payment_succeeded":
		return uc.handleInvoicePaymentSucceeded(ctx, event.Data)
	case "payment_intent.canceled":
		return uc.handlePaymentIntentCanceled(ctx, event.Data)
	case "payment_intent.payment_failed":
		return uc.handlePaymentIntentFailed(ctx, event.Data)
	case "payment_intent.requires_action":
		return uc.handlePaymentIntentRequiresAction(ctx, event.Data)
	case "payment_intent.succeeded":
		return uc.handlePaymentIntentSucceeded(ctx, event.Data)
	case "payment_method.attached", "payment_method.detached", "payment_method.updated",
		 "setup_intent.canceled", "setup_intent.requires_action", "setup_intent.setup_failed", "setup_intent.succeeded",
		 "customer.created", "customer.deleted", "customer.updated":
		return uc.paymentMethodUC.ProcessWebhook(ctx, event.Type, event.Data)
	default:
		return nil
	}
}

func (uc *paymentUsecase) handlePaymentIntentSucceeded(ctx context.Context, data map[string]interface{}) error {
	paymentIntentID, ok := data["id"].(string)
	if !ok {
		return errors.NewBadRequestError("invalid payment intent ID")
	}

	payment, err := uc.paymentRepo.GetByStripePaymentIntentID(ctx, paymentIntentID)
	if err != nil {
		return errors.NewNotFoundError("payment not found")
	}

	var receiptURL *string
	if url, ok := data["receipt_url"].(string); ok {
		receiptURL = &url
	}

	payment.ProcessPaymentIntentSucceeded(receiptURL)

	if err := uc.paymentRepo.Update(ctx, payment); err != nil {
		return errors.NewDatabaseError("failed to update payment", err)
	}

	return uc.handlePaymentCompleted(ctx, payment)
}

func (uc *paymentUsecase) handlePaymentIntentFailed(ctx context.Context, data map[string]interface{}) error {
	paymentIntentID, ok := data["id"].(string)
	if !ok {
		return errors.NewBadRequestError("invalid payment intent ID")
	}

	payment, err := uc.paymentRepo.GetByStripePaymentIntentID(ctx, paymentIntentID)
	if err != nil {
		return errors.NewNotFoundError("payment not found")
	}

	var lastPaymentError map[string]interface{}
	if lpe, ok := data["last_payment_error"].(map[string]interface{}); ok {
		lastPaymentError = lpe
	}

	payment.ProcessPaymentIntentFailed(lastPaymentError)

	if err := uc.paymentRepo.Update(ctx, payment); err != nil {
		return errors.NewDatabaseError("failed to update payment", err)
	}

	return uc.handlePaymentFailed(ctx, payment)
}

func (uc *paymentUsecase) handleChargeDisputeCreated(ctx context.Context, data map[string]interface{}) error {
	stripeDisputeID, ok := data["id"].(string)
	if !ok {
		return errors.NewBadRequestError("invalid dispute ID")
	}

	stripeChargeID, ok := data["charge"].(string)
	if !ok {
		return errors.NewBadRequestError("invalid charge ID")
	}

	amount, ok := data["amount"].(float64)
	if !ok {
		return errors.NewBadRequestError("invalid dispute amount")
	}

	currency, ok := data["currency"].(string)
	if !ok {
		currency = "JPY"
	}

	reason, ok := data["reason"].(string)
	if !ok {
		reason = "general"
	}

	status, ok := data["status"].(string)
	if !ok {
		status = "needs_response"
	}

	dispute, err := domain.NewDispute(stripeDisputeID, stripeChargeID, int(amount), currency, domain.DisputeReason(reason), domain.DisputeStatus(status))
	if err != nil {
		return errors.NewValidationError(err.Error())
	}

	if evidenceDueBy, ok := data["evidence_due_by"].(float64); ok {
		evidenceTime := time.Unix(int64(evidenceDueBy), 0)
		dispute.SetEvidenceDueBy(evidenceTime)
	}

	if isChargeRefundable, ok := data["is_charge_refundable"].(bool); ok {
		dispute.IsChargeRefundable = isChargeRefundable
	}

	if liveMode, ok := data["livemode"].(bool); ok {
		dispute.LiveMode = liveMode
	}

	if metadata, ok := data["metadata"].(map[string]interface{}); ok {
		metadataMap := make(map[string]string)
		for k, v := range metadata {
			if str, ok := v.(string); ok {
				metadataMap[k] = str
			}
		}
		dispute.Metadata = metadataMap
	}

	if networkReasonCode, ok := data["network_reason_code"].(string); ok {
		dispute.NetworkReasonCode = &networkReasonCode
	}

	if paymentIntentID, ok := data["payment_intent"].(string); ok {
		payment, err := uc.paymentRepo.GetByStripePaymentIntentID(ctx, paymentIntentID)
		if err == nil && payment != nil {
			dispute.SetPaymentID(payment.ID)
			uc.handleDisputeCreated(ctx, payment.UserID, dispute)
		}
	}

	return uc.disputeRepo.Create(ctx, dispute)
}

func (uc *paymentUsecase) handleDisputeCreated(ctx context.Context, userID uuid.UUID, dispute *domain.Dispute) {
	user, err := uc.userRepo.GetByID(ctx, userID)
	if err != nil {
		return
	}

	if user.ShouldReceiveEmailNotifications() {
		go uc.sendDisputeCreatedEmail(user, dispute)
	}
}

func (uc *paymentUsecase) sendDisputeCreatedEmail(user *domain.User, dispute *domain.Dispute) {
	subject := "Payment Dispute Created - Smooth Parking"
	var body string

	if user.PreferredLanguage == domain.LanguageCodeJapanese {
		body = fmt.Sprintf(`こんにちは %s さん,

お客様のお支払いに関して異議申し立てが作成されました。

争議ID: %s
金額: ¥%d
理由: %s

詳細については、カスタマーサポートまでお問い合わせください。

よろしくお願いいたします。
Smooth Parking チーム`, user.Name, dispute.StripeDisputeID, dispute.Amount, string(dispute.Reason))
	} else {
		body = fmt.Sprintf(`Hello %s,

A dispute has been created for your payment.

Dispute ID: %s
Amount: ¥%d
Reason: %s

Please contact customer support for more details.

Best regards,
Smooth Parking Team`, user.Name, dispute.StripeDisputeID, dispute.Amount, string(dispute.Reason))
	}

	uc.emailService.SendEmail(user.Email, subject, body)
}

func (uc *paymentUsecase) handleChargeFailed(ctx context.Context, data map[string]interface{}) error {
	_, ok := data["id"].(string)
	if !ok {
		return errors.NewBadRequestError("invalid charge ID")
	}

	paymentIntentID, ok := data["payment_intent"].(string)
	if !ok {
		return nil
	}

	payment, err := uc.paymentRepo.GetByStripePaymentIntentID(ctx, paymentIntentID)
	if err != nil {
		return nil
	}

	var failureCode, failureMessage *string
	if fc, ok := data["failure_code"].(string); ok {
		failureCode = &fc
	}
	if fm, ok := data["failure_message"].(string); ok {
		failureMessage = &fm
	}

	payment.ProcessChargeFailed(failureCode, failureMessage)

	if err := uc.paymentRepo.Update(ctx, payment); err != nil {
		return errors.NewDatabaseError("failed to update payment", err)
	}

	return uc.handlePaymentFailed(ctx, payment)
}

func (uc *paymentUsecase) handleChargeSucceeded(ctx context.Context, data map[string]interface{}) error {
	_, ok := data["id"].(string)
	if !ok {
		return errors.NewBadRequestError("invalid charge ID")
	}

	paymentIntentID, ok := data["payment_intent"].(string)
	if !ok {
		return nil
	}

	payment, err := uc.paymentRepo.GetByStripePaymentIntentID(ctx, paymentIntentID)
	if err != nil {
		return nil
	}

	var receiptURL *string
	if url, ok := data["receipt_url"].(string); ok {
		receiptURL = &url
	}

	var paymentMethodDetails map[string]interface{}
	if pmd, ok := data["payment_method_details"].(map[string]interface{}); ok {
		paymentMethodDetails = pmd
	}

	payment.ProcessChargeSucceeded(receiptURL, paymentMethodDetails)

	if err := uc.paymentRepo.Update(ctx, payment); err != nil {
		return errors.NewDatabaseError("failed to update payment", err)
	}

	return uc.handlePaymentCompleted(ctx, payment)
}







func (uc *paymentUsecase) handleInvoicePaymentFailed(ctx context.Context, data map[string]interface{}) error {
	stripeInvoiceID, ok := data["id"].(string)
	if !ok {
		return errors.NewBadRequestError("invalid invoice ID")
	}

	invoice, err := uc.invoiceRepo.GetByStripeInvoiceID(ctx, stripeInvoiceID)
	if err != nil {
		return nil
	}

	invoice.MarkAsFailed()
	if attemptCount, ok := data["attempt_count"].(float64); ok {
		invoice.AttemptCount = int(attemptCount)
	}
	invoice.Attempted = true

	return uc.invoiceRepo.Update(ctx, invoice)
}

func (uc *paymentUsecase) handleInvoicePaymentSucceeded(ctx context.Context, data map[string]interface{}) error {
	stripeInvoiceID, ok := data["id"].(string)
	if !ok {
		return errors.NewBadRequestError("invalid invoice ID")
	}

	invoice, err := uc.invoiceRepo.GetByStripeInvoiceID(ctx, stripeInvoiceID)
	if err != nil {
		return nil
	}

	if statusUpdateTime, ok := data["status_transitions"].(map[string]interface{}); ok {
		if paidAt, ok := statusUpdateTime["paid_at"].(float64); ok {
			paidTime := time.Unix(int64(paidAt), 0)
			invoice.MarkAsPaid(paidTime)
		}
	}

	if paymentIntentID, ok := data["payment_intent"].(string); ok {
		invoice.SetPaymentIntentID(paymentIntentID)
	}

	if attemptCount, ok := data["attempt_count"].(float64); ok {
		invoice.AttemptCount = int(attemptCount)
	}
	invoice.Attempted = true

	return uc.invoiceRepo.Update(ctx, invoice)
}

func (uc *paymentUsecase) handlePaymentIntentCanceled(ctx context.Context, data map[string]interface{}) error {
	paymentIntentID, ok := data["id"].(string)
	if !ok {
		return errors.NewBadRequestError("invalid payment intent ID")
	}

	payment, err := uc.paymentRepo.GetByStripePaymentIntentID(ctx, paymentIntentID)
	if err != nil {
		return nil
	}

	payment.ProcessPaymentIntentCanceled()

	if err := uc.paymentRepo.Update(ctx, payment); err != nil {
		return errors.NewDatabaseError("failed to update payment", err)
	}

	return uc.handlePaymentFailed(ctx, payment)
}

func (uc *paymentUsecase) handlePaymentIntentRequiresAction(ctx context.Context, data map[string]interface{}) error {
	paymentIntentID, ok := data["id"].(string)
	if !ok {
		return errors.NewBadRequestError("invalid payment intent ID")
	}

	payment, err := uc.paymentRepo.GetByStripePaymentIntentID(ctx, paymentIntentID)
	if err != nil {
		return nil
	}

	payment.ProcessPaymentIntentRequiresAction()
	return uc.paymentRepo.Update(ctx, payment)
}





func (uc *paymentUsecase) GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*domain.Payment, error) {
	return uc.paymentRepo.GetByUserID(ctx, userID, limit, offset)
}

func (uc *paymentUsecase) ProcessAutoPayment(ctx context.Context, sessionID uuid.UUID) (*domain.Payment, error) {
	session, err := uc.sessionRepo.GetByID(ctx, sessionID)
	if err != nil {
		return nil, errors.NewNotFoundError("session not found")
	}

	if session.Status != domain.SessionStatusCompleted {
		return nil, errors.NewBadRequestError("session must be completed to process auto payment")
	}

	if session.UserID == nil {
		return nil, errors.NewBadRequestError("session has no associated user")
	}

	user, err := uc.userRepo.GetByID(ctx, *session.UserID)
	if err != nil {
		return nil, errors.NewNotFoundError("user not found")
	}

	if !user.AutoPaymentEnabled {
		return nil, errors.NewBadRequestError("auto payment is not enabled for this user")
	}

	if user.DefaultPaymentMethodID == nil {
		return nil, errors.NewBadRequestError("user has no default payment method")
	}

	defaultPaymentMethodID, err := uuid.Parse(*user.DefaultPaymentMethodID)
	if err != nil {
		return nil, errors.NewBadRequestError("invalid default payment method ID")
	}

	paymentMethod, err := uc.paymentMethodRepo.GetByID(ctx, defaultPaymentMethodID)
	if err != nil {
		return nil, errors.NewNotFoundError("default payment method not found")
	}

	if !paymentMethod.CanBeUsedForPayment() {
		return nil, errors.NewBadRequestError("default payment method cannot be used for payment")
	}

	amount := session.GetFinalAmount()
	if amount <= 0 {
		return nil, errors.NewBadRequestError("invalid session fee amount")
	}

	existingPayment, err := uc.paymentRepo.GetBySessionID(ctx, sessionID)
	if err == nil && existingPayment != nil && existingPayment.IsCompleted() {
		return existingPayment, nil
	}

	payment, err := domain.NewPayment(sessionID, *session.UserID, amount)
	if err != nil {
		return nil, errors.NewValidationError(err.Error())
	}

	if user.StripeCustomerID == nil {
		return nil, errors.NewBadRequestError("user has no Stripe customer ID")
	}

	paymentIntent, err := uc.stripeGateway.CreatePaymentIntent(ctx, amount, "JPY", *user.StripeCustomerID, paymentMethod.StripePaymentMethodID)
	if err != nil {
		return nil, errors.NewExternalServiceError("failed to create payment intent", err)
	}

	payment.SetStripePaymentIntentID(paymentIntent.ID)
	payment.MarkAsProcessing()

	if paymentMethod.Brand != nil && paymentMethod.Last4 != nil {
		payment.SetPaymentMethod(string(paymentMethod.Type), *paymentMethod.Last4, *paymentMethod.Brand)
	}

	if existingPayment != nil {
		payment.ID = existingPayment.ID
		if err := uc.paymentRepo.Update(ctx, payment); err != nil {
			return nil, errors.NewDatabaseError("failed to update payment", err)
		}
	} else {
		if err := uc.paymentRepo.Create(ctx, payment); err != nil {
			return nil, errors.NewDatabaseError("failed to create payment", err)
		}
	}

	confirmResult, err := uc.stripeGateway.ConfirmPaymentIntent(ctx, paymentIntent.ID)
	if err != nil {
		payment.MarkAsFailed("Failed to confirm payment")
		uc.paymentRepo.Update(ctx, payment)
		return nil, errors.NewExternalServiceError("failed to confirm payment intent", err)
	}

	if confirmResult.Status == "succeeded" {
		payment.MarkAsCompleted()
		if confirmResult.ReceiptURL != "" {
			payment.SetReceiptURL(confirmResult.ReceiptURL)
		}
	} else if confirmResult.Status == "requires_action" {
		return nil, errors.NewBadRequestError("payment requires additional authentication")
	} else {
		payment.MarkAsFailed("Payment confirmation failed")
	}

	if err := uc.paymentRepo.Update(ctx, payment); err != nil {
		return nil, errors.NewDatabaseError("failed to update payment status", err)
	}

	return payment, nil
}

func (uc *paymentUsecase) handlePaymentCompleted(ctx context.Context, payment *domain.Payment) error {
	session, err := uc.sessionRepo.GetByID(ctx, payment.SessionID)
	if err != nil {
		return nil
	}

	session.MarkAsPaid()
	if err := uc.sessionRepo.Update(ctx, session); err != nil {
		return errors.NewDatabaseError("failed to update session", err)
	}

	user, err := uc.userRepo.GetByID(ctx, payment.UserID)
	if err != nil {
		return nil
	}

	if user.ShouldReceiveEmailNotifications() {
		go uc.sendPaymentCompletedEmail(user, payment, session)
	}

	uc.createPaymentNotification(ctx, payment.UserID, payment)

	return nil
}

func (uc *paymentUsecase) handlePaymentFailed(ctx context.Context, payment *domain.Payment) error {
	user, err := uc.userRepo.GetByID(ctx, payment.UserID)
	if err != nil {
		return nil
	}

	if user.ShouldReceiveEmailNotifications() {
		go uc.sendPaymentFailedEmail(user, payment)
	}

	return nil
}

func (uc *paymentUsecase) sendPaymentCompletedEmail(user *domain.User, payment *domain.Payment, session *domain.Session) {
	subject := "Payment Completed - Smooth Parking"
	var body string

	if user.PreferredLanguage == domain.LanguageCodeJapanese {
		body = fmt.Sprintf(`こんにちは %s さん,

駐車料金のお支払いが完了いたしました。

金額: %s
支払い日時: %s

ご利用ありがとうございました。

よろしくお願いいたします。
Smooth Parking チーム`, user.Name, payment.GetAmountInCurrency(), payment.PaidAt.Format("2006-01-02 15:04:05"))
	} else {
		body = fmt.Sprintf(`Hello %s,

Your parking payment has been completed successfully.

Amount: %s
Payment Date: %s

Thank you for using Smooth Parking.

Best regards,
Smooth Parking Team`, user.Name, payment.GetAmountInCurrency(), payment.PaidAt.Format("2006-01-02 15:04:05"))
	}

	uc.emailService.SendEmail(user.Email, subject, body)
}

func (uc *paymentUsecase) sendPaymentFailedEmail(user *domain.User, payment *domain.Payment) {
	uc.emailService.SendPaymentFailedEmail(user, payment)
}

func (uc *paymentUsecase) createPaymentNotification(ctx context.Context, userID uuid.UUID, payment *domain.Payment) {
	title := "Payment Completed"
	message := fmt.Sprintf("Your parking payment of %s has been processed successfully.", payment.GetAmountInCurrency())

	notification, err := domain.NewNotification(userID, domain.NotificationTypePaymentCompleted, title, message)
	if err != nil {
		return
	}

	notification.SetPaymentID(payment.ID)
	uc.notificationRepo.Create(ctx, notification)
}


