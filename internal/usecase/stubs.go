package usecase

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/smooth-inc/backend/internal/domain"
)

// Stub implementations that satisfy the interfaces but return not implemented errors
// These will be replaced with actual implementations later

// authUsecaseStub is a stub implementation of AuthUsecase
type authUsecaseStub struct{}

func NewAuthUsecaseStub() AuthUsecase {
	return &authUsecaseStub{}
}

func (a *authUsecaseStub) Register(ctx context.Context, req *domain.RegisterRequest) (*domain.User, error) {
	return nil, fmt.Errorf("auth register not implemented yet")
}

func (a *authUsecaseStub) Login(ctx context.Context, req *domain.LoginRequest, ipAddress, userAgent string) (*domain.AuthResponse, error) {
	return nil, fmt.Errorf("auth login not implemented yet")
}

func (a *authUsecaseStub) RefreshToken(ctx context.Context, req *domain.RefreshTokenRequest, ipAddress, userAgent string) (*domain.TokenPair, error) {
	return nil, fmt.Errorf("auth refresh token not implemented yet")
}

func (a *authUsecaseStub) VerifyEmail(ctx context.Context, req *domain.EmailVerificationRequest) (*domain.AuthResponse, error) {
	return nil, fmt.Errorf("auth verify email not implemented yet")
}

func (a *authUsecaseStub) ResendEmailVerification(ctx context.Context, req *domain.ResendVerificationRequest) error {
	return fmt.Errorf("auth resend email verification not implemented yet")
}

func (a *authUsecaseStub) ForgotPassword(ctx context.Context, req *domain.ForgotPasswordRequest) error {
	return fmt.Errorf("auth forgot password not implemented yet")
}

func (a *authUsecaseStub) ResetPassword(ctx context.Context, req *domain.ResetPasswordRequest) error {
	return fmt.Errorf("auth reset password not implemented yet")
}

func (a *authUsecaseStub) ChangePassword(ctx context.Context, userID uuid.UUID, req *domain.ChangePasswordRequest) error {
	return fmt.Errorf("auth change password not implemented yet")
}

func (a *authUsecaseStub) GetUserSessions(ctx context.Context, userID uuid.UUID, currentSessionID uuid.UUID) (*domain.SessionsResponse, error) {
	return nil, fmt.Errorf("auth get user sessions not implemented yet")
}

func (a *authUsecaseStub) LogoutSession(ctx context.Context, userID uuid.UUID, sessionID uuid.UUID) error {
	return fmt.Errorf("auth logout session not implemented yet")
}

func (a *authUsecaseStub) LogoutAllOtherSessions(ctx context.Context, userID uuid.UUID, currentSessionID uuid.UUID) error {
	return fmt.Errorf("auth logout all other sessions not implemented yet")
}

// plateUsecaseStub is a stub implementation of PlateUsecase
type plateUsecaseStub struct{}

func NewPlateUsecaseStub() PlateUsecase {
	return &plateUsecaseStub{}
}

func (p *plateUsecaseStub) Create(ctx context.Context, userID uuid.UUID, region, classification, hiragana, serialNumber string, plateType domain.PlateType) (*domain.Plate, error) {
	return nil, fmt.Errorf("plate create not implemented yet")
}

func (p *plateUsecaseStub) GetByUserID(ctx context.Context, userID uuid.UUID) ([]*domain.Plate, error) {
	return nil, fmt.Errorf("plate get by user ID not implemented yet")
}

func (p *plateUsecaseStub) GetByID(ctx context.Context, id uuid.UUID) (*domain.Plate, error) {
	return nil, fmt.Errorf("plate get by ID not implemented yet")
}

func (p *plateUsecaseStub) Delete(ctx context.Context, id uuid.UUID, userID uuid.UUID) error {
	return fmt.Errorf("plate delete not implemented yet")
}

// parkingLotUsecaseStub is a stub implementation of ParkingLotUsecase
type parkingLotUsecaseStub struct{}

func NewParkingLotUsecaseStub() ParkingLotUsecase {
	return &parkingLotUsecaseStub{}
}

func (pl *parkingLotUsecaseStub) GetByID(ctx context.Context, id uuid.UUID) (*domain.ParkingLot, error) {
	return nil, fmt.Errorf("parking lot get by ID not implemented yet")
}

func (pl *parkingLotUsecaseStub) List(ctx context.Context, limit, offset int) ([]*domain.ParkingLot, error) {
	return nil, fmt.Errorf("parking lot list not implemented yet")
}

func (pl *parkingLotUsecaseStub) SearchNearby(ctx context.Context, lat, lng float64, radius int, limit, offset int) ([]*domain.ParkingLot, error) {
	return nil, fmt.Errorf("parking lot search nearby not implemented yet")
}

func (pl *parkingLotUsecaseStub) GetAvailability(ctx context.Context, lotID uuid.UUID) (int, error) {
	return 0, fmt.Errorf("parking lot get availability not implemented yet")
}

// sessionUsecaseStub is a stub implementation of SessionUsecase
type sessionUsecaseStub struct{}

func NewSessionUsecaseStub() SessionUsecase {
	return &sessionUsecaseStub{}
}

func (s *sessionUsecaseStub) Start(ctx context.Context, parkingLotID uuid.UUID, plateID *uuid.UUID, userID *uuid.UUID) (*domain.Session, error) {
	return nil, fmt.Errorf("session start not implemented yet")
}

func (s *sessionUsecaseStub) Complete(ctx context.Context, sessionID uuid.UUID, exitTime string) error {
	return fmt.Errorf("session complete not implemented yet")
}

func (s *sessionUsecaseStub) GetByID(ctx context.Context, id uuid.UUID) (*domain.Session, error) {
	return nil, fmt.Errorf("session get by ID not implemented yet")
}

func (s *sessionUsecaseStub) GetActiveByPlateID(ctx context.Context, plateID uuid.UUID) (*domain.Session, error) {
	return nil, fmt.Errorf("session get active by plate ID not implemented yet")
}

func (s *sessionUsecaseStub) GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*domain.Session, error) {
	return nil, fmt.Errorf("session get by user ID not implemented yet")
}

// paymentUsecaseStub is a stub implementation of PaymentUsecase
type paymentUsecaseStub struct{}

func NewPaymentUsecaseStub() PaymentUsecase {
	return &paymentUsecaseStub{}
}

func (p *paymentUsecaseStub) CreatePaymentLink(ctx context.Context, sessionID uuid.UUID) (string, error) {
	return "", fmt.Errorf("payment create payment link not implemented yet")
}

func (p *paymentUsecaseStub) ProcessWebhook(ctx context.Context, payload []byte, signature string) error {
	return fmt.Errorf("payment process webhook not implemented yet")
}

func (p *paymentUsecaseStub) GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*domain.Payment, error) {
	return nil, fmt.Errorf("payment get by user ID not implemented yet")
}

func (p *paymentUsecaseStub) ProcessAutoPayment(ctx context.Context, sessionID uuid.UUID) (*domain.Payment, error) {
	return nil, fmt.Errorf("payment process auto payment not implemented yet")
}

// bookingUsecaseStub is a stub implementation of BookingUsecase
type bookingUsecaseStub struct{}

func NewBookingUsecaseStub() BookingUsecase {
	return &bookingUsecaseStub{}
}

func (b *bookingUsecaseStub) Create(ctx context.Context, userID, plateID, parkingLotID uuid.UUID, startTime, endTime string) (*domain.Booking, error) {
	return nil, fmt.Errorf("booking create not implemented yet")
}

func (b *bookingUsecaseStub) GetByID(ctx context.Context, id uuid.UUID) (*domain.Booking, error) {
	return nil, fmt.Errorf("booking get by ID not implemented yet")
}

func (b *bookingUsecaseStub) GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*domain.Booking, error) {
	return nil, fmt.Errorf("booking get by user ID not implemented yet")
}

func (b *bookingUsecaseStub) Cancel(ctx context.Context, bookingID uuid.UUID, userID uuid.UUID) error {
	return fmt.Errorf("booking cancel not implemented yet")
}

// notificationUsecaseStub is a stub implementation of NotificationUsecase
type notificationUsecaseStub struct{}

func NewNotificationUsecaseStub() NotificationUsecase {
	return &notificationUsecaseStub{}
}

func (n *notificationUsecaseStub) Create(ctx context.Context, userID uuid.UUID, notificationType domain.NotificationType, title, message string) (*domain.Notification, error) {
	return nil, fmt.Errorf("notification create not implemented yet")
}

func (n *notificationUsecaseStub) GetByUserID(ctx context.Context, userID uuid.UUID, unreadOnly bool, limit, offset int) ([]*domain.Notification, error) {
	return nil, fmt.Errorf("notification get by user ID not implemented yet")
}

func (n *notificationUsecaseStub) MarkAsRead(ctx context.Context, notificationID uuid.UUID, userID uuid.UUID) error {
	return fmt.Errorf("notification mark as read not implemented yet")
}

func (n *notificationUsecaseStub) SendPendingNotifications(ctx context.Context) error {
	return fmt.Errorf("notification send pending not implemented yet")
}

// hardwareUsecaseStub is a stub implementation of HardwareUsecase
type hardwareUsecaseStub struct{}

func NewHardwareUsecaseStub() HardwareUsecase {
	return &hardwareUsecaseStub{}
}

func (h *hardwareUsecaseStub) ProcessDetection(ctx context.Context, plateNumber string, parkingLotID uuid.UUID, confidence float64, imageURL *string) error {
	return fmt.Errorf("hardware process detection not implemented yet")
}
